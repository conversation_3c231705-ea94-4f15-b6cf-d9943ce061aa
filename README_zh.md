![](assets/logo.png?v=1&type=image)
<div align="center">
<h3>Mobile-Agent: 强大的移动设备操作助手家族<h3>
<div align="center">
	<a href="https://huggingface.co/spaces/junyangwang0410/PC-Agent"><img src="https://huggingface.co/datasets/huggingface/badges/raw/main/open-in-hf-spaces-sm-dark.svg" alt="Open in Spaces"></a>
	<a href="https://www.modelscope.cn/studios/wangjunyang/PC-Agent"><img src="assets/Demo-ModelScope-brightgreen.svg" alt="Demo ModelScope"></a>
  <a href="https://arxiv.org/abs/2502.14282 "><img src="https://img.shields.io/badge/Arxiv-2502.14282-b31b1b.svg?logo=arXiv" alt=""></a>
  <a href="https://arxiv.org/abs/2501.11733"><img src="https://img.shields.io/badge/Arxiv-2501.11733-b31b1b.svg?logo=arXiv" alt=""></a>
  <a href="https://arxiv.org/abs/2406.01014 "><img src="https://img.shields.io/badge/Arxiv-2406.01014-b31b1b.svg?logo=arXiv" alt=""></a>
  <a href="https://arxiv.org/abs/2401.16158"><img src="https://img.shields.io/badge/Arxiv-2401.16158-b31b1b.svg?logo=arXiv" alt=""></a>
</div>
<p align="center">
<a href="https://trendshift.io/repositories/7423" target="_blank"><img src="https://trendshift.io/api/badge/repositories/7423" alt="MobileAgent | Trendshift" style="width: 250px; height: 55px;" width="250" height="55"/></a>
</p>
</div>

<div align="center">
<a href="README_zh.md">简体中文</a> | <a href="README.md">English</a> | <a href="README_ja.md">日本語</a>
<hr>
</div>
<!--
简体中文 | [English](README.md) | [日本語](README_ja.md)
<hr>
-->

## 📺Demo

### Newest PC-Agent
细节见[论文](https://arxiv.org/abs/2502.14282)。

在Hugging Face Space上体验[demo](https://huggingface.co/spaces/junyangwang0410/PC-Agent)。

在ModelScope上体验[demo](https://www.modelscope.cn/studios/wangjunyang/PC-Agent)。

https://github.com/user-attachments/assets/b13bbb14-b39a-4c6b-b4a6-3df97de517dc

### Mobile-Agent-E
细节见[项目主页](https://x-plug.github.io/MobileAgent)。

<!-- <div style="display: flex; justify-content: space-between; gap: 10px; flex-wrap: wrap;">
  <video width="30%" controls>
    <source src="https://raw.githubusercontent.com/X-PLUG/MobileAgent/main/Mobile-Agent-E/static/videos/bouldering_gym.mp4" type="video/mp4">
  </video>
  <video width="30%" controls>
    <source src="https://raw.githubusercontent.com/X-PLUG/MobileAgent/main/Mobile-Agent-E/static/videos/shopping.mp4" type="video/mp4">
  </video>
  <video width="30%" controls>
    <source src="https://raw.githubusercontent.com/X-PLUG/MobileAgent/main/Mobile-Agent-E/static/videos/survey.mp4" type="video/mp4">
  </video>
</div> -->

### Mobile-Agent-v3（注意：该视频没有加速处理）
**YouTube**

[![YouTube](https://img.youtube.com/vi/EMbIpzqJld0/0.jpg)](https://www.youtube.com/watch?v=EMbIpzqJld0)

**哔哩哔哩**

[![Bilibili](https://img.youtube.com/vi/EMbIpzqJld0/0.jpg)](https://www.bilibili.com/video/BV1pPvyekEsa/?share_source=copy_web&vd_source=47ffcd57083495a8965c8cdbe1a751ae)

### PC-Agent
**谷歌浏览器与钉钉**

https://github.com/user-attachments/assets/b890a08f-8a2f-426d-9458-aa3699185030

**Word**

https://github.com/user-attachments/assets/37f0a0a5-3d21-4232-9d1d-0fe845d0f77d

### Mobile-Agent-v2
https://github.com/X-PLUG/MobileAgent/assets/127390760/d907795d-b5b9-48bf-b1db-70cf3f45d155

### Mobile-Agent
https://github.com/X-PLUG/MobileAgent/assets/127390760/26c48fb0-67ed-4df6-97b2-aa0c18386d31


## 📢新闻
* 🔥🔥[2.21.25] 我们发布了 PC-Agent 的更新版本。详情请查看[论文](https://arxiv.org/abs/2502.14282)。代码将很快更新。
* 🔥🔥[1.20.25] 我们发布了 [Mobile-Agent-E](https://x-plug.github.io/MobileAgent)，这是一个分层的多代理框架，能够通过过去的经验进行自我进化，在复杂的多应用任务上实现更强大的性能。
* 🔥🔥[9.26] Mobile-Agent-v2 被 **The Thirty-eighth Annual Conference on Neural Information Processing Systems (NeurIPS 2024)** 接收。
* 🔥[8.23]我们发布了一个支持Mac和Windows平台的**PC**操作助手PC-Agent, 通过Mobile-Agent-v2框架实现。
* 🔥[7.29] Mobile-Agent获得了 ***第二十三届中国计算语言学大会*** (CCL 2024) 的 **最佳demo奖项**。在CCL 2024上，我们展示了即将开源的Mobile-Agent-v3，拥有更小的内存开销（8 GB）、更快的推理速度（每次操作10-15秒），并且使用开源模型。视频Demo请见上一个板块📺Demo。
* [6.27] 我们在[Hugging Face](https://huggingface.co/spaces/junyangwang0410/Mobile-Agent)和[ModelScope](https://modelscope.cn/studios/wangjunyang/Mobile-Agent-v2)发布了可以上传手机截图体验Mobile-Agent-v2的Demo，无需配置模型和设备，即刻便可体验。
* [6. 4] Modelscope-Agent 已经支持 Mobile-Agent-V2，基于 Android Adb Env，请查看 [application](https://github.com/modelscope/modelscope-agent/tree/master/apps/mobile_agent)。
* [6. 4] 我们发布了新一代移动设备操作助手 Mobile-Agent-v2, 通过多智能体协作实现有效导航。
* [3.10] Mobile-Agent 被 **ICLR 2024 Workshop on Large Language Model (LLM) Agents** 接收。

## 📱版本
* [PC-Agent](PC-Agent/README.md) - 用于 PC 上复杂任务自动化的分层多代理协作框架
* [Mobile-Agent-E](Mobile-Agent-E/README.md) - 在复杂、长期、推理密集型任务上具有更强的性能，具有自我进化能力
* [Mobile-Agent-v3](Mobile-Agent-v3/README_zh.md)
* [Mobile-Agent-v2](Mobile-Agent-v2/README_zh.md) - 通过多代理协作有效导航的移动设备操作助手
* [Mobile-Agent](Mobile-Agent/README_zh.md) - 视觉感知方案的自动化移动设备操作智能体

## ⭐Star历史
[![Star History Chart](https://api.star-history.com/svg?repos=X-PLUG/MobileAgent&type=Date)](https://star-history.com/#X-PLUG/MobileAgent&Date)

## 引用
如果您发现 Mobile-Agent 对您的研究和应用有用，请使用此 BibTeX 进行引用：
```
@article{liu2025pc,
  title={PC-Agent: A Hierarchical Multi-Agent Collaboration Framework for Complex Task Automation on PC},
  author={Liu, Haowei and Zhang, Xi and Xu, Haiyang and Wanyan, Yuyang and Wang, Junyang and Yan, Ming and Zhang, Ji and Yuan, Chunfeng and Xu, Changsheng and Hu, Weiming and Huang, Fei},
  journal={arXiv preprint arXiv:2502.14282},
  year={2025}
}

@article{wang2025mobile,
  title={Mobile-Agent-E: Self-Evolving Mobile Assistant for Complex Tasks},
  author={Wang, Zhenhailong and Xu, Haiyang and Wang, Junyang and Zhang, Xi and Yan, Ming and Zhang, Ji and Huang, Fei and Ji, Heng},
  journal={arXiv preprint arXiv:2501.11733},
  year={2025}
}

@article{wang2024mobile2,
  title={Mobile-Agent-v2: Mobile Device Operation Assistant with Effective Navigation via Multi-Agent Collaboration},
  author={Wang, Junyang and Xu, Haiyang and Jia, Haitao and Zhang, Xi and Yan, Ming and Shen, Weizhou and Zhang, Ji and Huang, Fei and Sang, Jitao},
  journal={arXiv preprint arXiv:2406.01014},
  year={2024}
}

@article{wang2024mobile,
  title={Mobile-Agent: Autonomous Multi-Modal Mobile Device Agent with Visual Perception},
  author={Wang, Junyang and Xu, Haiyang and Ye, Jiabo and Yan, Ming and Shen, Weizhou and Zhang, Ji and Huang, Fei and Sang, Jitao},
  journal={arXiv preprint arXiv:2401.16158},
  year={2024}
}
```

## 📦相关项目
* [AppAgent: Multimodal Agents as Smartphone Users](https://github.com/mnotgod96/AppAgent)
* [mPLUG-Owl & mPLUG-Owl2: Modularized Multimodal Large Language Model](https://github.com/X-PLUG/mPLUG-Owl)
* [Qwen-VL: A Versatile Vision-Language Model for Understanding, Localization, Text Reading, and Beyond](https://github.com/QwenLM/Qwen-VL)
* [GroundingDINO: Marrying DINO with Grounded Pre-Training for Open-Set Object Detection](https://github.com/IDEA-Research/GroundingDINO)
* [CLIP: Contrastive Language-Image Pretraining](https://github.com/openai/CLIP)
