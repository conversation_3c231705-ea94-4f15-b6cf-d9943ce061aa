![](assets/logo.png?v=1&type=image)
<div align="center">
<h3>Mobile-Agent: 強力なモバイルデバイス操作アシスタントファミリー<h3>
<div align="center">
	<a href="https://huggingface.co/spaces/junyangwang0410/PC-Agent"><img src="https://huggingface.co/datasets/huggingface/badges/raw/main/open-in-hf-spaces-sm-dark.svg" alt="Open in Spaces"></a>
	<a href="https://www.modelscope.cn/studios/wangjunyang/PC-Agent"><img src="assets/Demo-ModelScope-brightgreen.svg" alt="Demo ModelScope"></a>
  <a href="https://arxiv.org/abs/2502.14282 "><img src="https://img.shields.io/badge/Arxiv-2502.14282-b31b1b.svg?logo=arXiv" alt=""></a>
  <a href="https://arxiv.org/abs/2501.11733"><img src="https://img.shields.io/badge/Arxiv-2501.11733-b31b1b.svg?logo=arXiv" alt=""></a>
  <a href="https://arxiv.org/abs/2406.01014 "><img src="https://img.shields.io/badge/Arxiv-2406.01014-b31b1b.svg?logo=arXiv" alt=""></a>
  <a href="https://arxiv.org/abs/2401.16158"><img src="https://img.shields.io/badge/Arxiv-2401.16158-b31b1b.svg?logo=arXiv" alt=""></a>
</div>
<p align="center">
<a href="https://trendshift.io/repositories/7423" target="_blank"><img src="https://trendshift.io/api/badge/repositories/7423" alt="MobileAgent | Trendshift" style="width: 250px; height: 55px;" width="250" height="55"/></a>
</p>
</div>

<div align="center">
<a href="README_ja.md">日本語</a> | <a href="README.md">English</a> | <a href="README_zh.md">简体中文</a>
<hr>
</div>
<!--
日本語 | [English](README.md) | [简体中文](README_zh.md)
<hr>
-->

## 📺デモ

### Newest PC-Agent
詳細は[論文](https://arxiv.org/abs/2502.14282)をご覧ください。

Hugging Face Spaceの[デモ](https://huggingface.co/spaces/junyangwang0410/PC-Agent)をお試しください。

ModelScopeの[デモ](https://www.modelscope.cn/studios/wangjunyang/PC-Agent)をお試しください。

### Mobile-Agent-E
詳細については、[プロジェクトのホームページ](https://x-plug.github.io/MobileAgent)を参照してください。

<!-- <div style="display: flex; justify-content: space-between; gap: 10px; flex-wrap: wrap;">
  <video width="30%" controls>
    <source src="https://raw.githubusercontent.com/X-PLUG/MobileAgent/main/Mobile-Agent-E/static/videos/bouldering_gym.mp4" type="video/mp4">
  </video>
  <video width="30%" controls>
    <source src="https://raw.githubusercontent.com/X-PLUG/MobileAgent/main/Mobile-Agent-E/static/videos/shopping.mp4" type="video/mp4">
  </video>
  <video width="30%" controls>
    <source src="https://raw.githubusercontent.com/X-PLUG/MobileAgent/main/Mobile-Agent-E/static/videos/survey.mp4" type="video/mp4">
  </video>
</div> -->

### Mobile-Agent-v3（注：ビデオは加速されていません）
**YouTube**

[![YouTube](https://img.youtube.com/vi/EMbIpzqJld0/0.jpg)](https://www.youtube.com/watch?v=EMbIpzqJld0)

**Bilibili**

[![Bilibili](https://img.youtube.com/vi/EMbIpzqJld0/0.jpg)](https://www.bilibili.com/video/BV1pPvyekEsa/?share_source=copy_web&vd_source=47ffcd57083495a8965c8cdbe1a751ae)

### PC-Agent
**Chrome and DingTalk**

https://github.com/user-attachments/assets/b890a08f-8a2f-426d-9458-aa3699185030

**Word**

https://github.com/user-attachments/assets/37f0a0a5-3d21-4232-9d1d-0fe845d0f77d

### Mobile-Agent-v2
https://github.com/X-PLUG/MobileAgent/assets/127390760/d907795d-b5b9-48bf-b1db-70cf3f45d155

### Mobile-Agent
https://github.com/X-PLUG/MobileAgent/assets/127390760/26c48fb0-67ed-4df6-97b2-aa0c18386d31


## 📢ニュース
* 🔥🔥[2.21.25] PC-Agent の更新バージョンをリリースしました。詳細については[論文](https://arxiv.org/abs/2502.14282)を確認してください。コードは近日中に更新される予定です。
* 🔥🔥[1.20.25] 私たちは、過去の経験を通じて自己進化し、複雑なマルチアプリタスクでより強力なパフォーマンスを実現できる階層型マルチエージェントフレームワークである [Mobile-Agent-E](https://x-plug.github.io/MobileAgent)を提案します。
* 🔥🔥[9.26] Mobile-Agent-v2 は **The Thirty-eighth Annual Conference on Neural Information Processing Systems (NeurIPS 2024)** によって承認されました。
* 🔥[8.23] MacとWindowsプラットフォームに対応したPC操作アシスタント「PC-Agent」をリリースしました。
* 🔥[7.29] Mobile-Agent、***計算言語学に関する第23回中国全国会議***（CCL 2024）で**ベストデモ賞**を受賞しました。 CCL 2024では、今後のMobile-Agent-V3を示しました。メモリオーバーヘッド（8 GB）が小さく、推論速度が高く（操作あたり10S-15S）、すべてオープンソースモデルを使用しています。ビデオデモ、セクション📺Demoを参照してください。
* [6.27] [Hugging Face](https://huggingface.co/spaces/junyangwang0410/Mobile-Agent)と[ModelScope](https://modelscope.cn/studios/wangjunyang/Mobile-Agent-v2)で、Mobile-Agent-v2のデモを公開しました。携帯電話のスクリーンショットをアップロードして体験できます。モデルやデバイスの設定は不要です。
* [6. 4] Modelscope-Agentは、Android Adb Envに基づいてMobile-Agent-V2をサポートしています。詳細は[アプリケーション](https://github.com/modelscope/modelscope-agent/tree/master/apps/mobile_agent)をご覧ください。
* [6. 4] 新世代のモバイルデバイス操作アシスタント Mobile-Agent-v2を発表しました。マルチエージェント協力により効果的なナビゲーションを実現します。
* [3.10] Mobile-Agentは**ICLR 2024 Workshop on Large Language Model (LLM) Agents**に採択されました。

## 📱バージョン
* [PC-Agent](PC-Agent/README.md) - PC 上の複雑なタスクを自動化するための階層型マルチエージェント コラボレーション フレームワーク
* [Mobile-Agent-E](Mobile-Agent-E/README.md) - 自己進化機能により、複雑で長期にわたる推論集中型のタスクのパフォーマンスを強化
* [Mobile-Agent-v3](Mobile-Agent-v3/README.md)
* [Mobile-Agent-v2](Mobile-Agent-v2/README_ja.md) - マルチエージェント協力による効果的なナビゲーションを実現するモバイルデバイス操作アシスタント
* [Mobile-Agent](Mobile-Agent/README_ja.md) - 視覚認識を備えた自律型マルチモーダルモバイルデバイスエージェント

## ⭐スター履歴
[![Star History Chart](https://api.star-history.com/svg?repos=X-PLUG/MobileAgent&type=Date)](https://star-history.com/#X-PLUG/MobileAgent&Date)

## 📑引用
Mobile-Agentが研究やアプリケーションに役立つ場合は、次のBibTeXを使用して引用してください：
```
@article{liu2025pc,
  title={PC-Agent: A Hierarchical Multi-Agent Collaboration Framework for Complex Task Automation on PC},
  author={Liu, Haowei and Zhang, Xi and Xu, Haiyang and Wanyan, Yuyang and Wang, Junyang and Yan, Ming and Zhang, Ji and Yuan, Chunfeng and Xu, Changsheng and Hu, Weiming and Huang, Fei},
  journal={arXiv preprint arXiv:2502.14282},
  year={2025}
}

@article{wang2025mobile,
  title={Mobile-Agent-E: Self-Evolving Mobile Assistant for Complex Tasks},
  author={Wang, Zhenhailong and Xu, Haiyang and Wang, Junyang and Zhang, Xi and Yan, Ming and Zhang, Ji and Huang, Fei and Ji, Heng},
  journal={arXiv preprint arXiv:2501.11733},
  year={2025}
}

@article{wang2024mobile2,
  title={Mobile-Agent-v2: Mobile Device Operation Assistant with Effective Navigation via Multi-Agent Collaboration},
  author={Wang, Junyang and Xu, Haiyang and Jia, Haitao and Zhang, Xi and Yan, Ming and Shen, Weizhou and Zhang, Ji and Huang, Fei and Sang, Jitao},
  journal={arXiv preprint arXiv:2406.01014},
  year={2024}
}

@article{wang2024mobile,
  title={Mobile-Agent: Autonomous Multi-Modal Mobile Device Agent with Visual Perception},
  author={Wang, Junyang and Xu, Haiyang and Ye, Jiabo and Yan, Ming and Shen, Weizhou and Zhang, Ji and Huang, Fei and Sang, Jitao},
  journal={arXiv preprint arXiv:2401.16158},
  year={2024}
}
```

## 📦関連プロジェクト
* [AppAgent: Multimodal Agents as Smartphone Users](https://github.com/mnotgod96/AppAgent)
* [mPLUG-Owl & mPLUG-Owl2: Modularized Multimodal Large Language Model](https://github.com/X-PLUG/mPLUG-Owl)
* [Qwen-VL: A Versatile Vision-Language Model for Understanding, Localization, Text Reading, and Beyond](https://github.com/QwenLM/Qwen-VL)
* [GroundingDINO: Marrying DINO with Grounded Pre-Training for Open-Set Object Detection](https://github.com/IDEA-Research/GroundingDINO)
* [CLIP: Contrastive Language-Image Pretraining](https://github.com/openai/CLIP)
