"In the Notepad app, open the 'memo' file in 'Documents', and check the second event in the morning. Set an alarm 1 hour before this event in the Clock app.",
"In the Notepad app, open the 'memo' file in 'Documents', and check the location of the meeting with <PERSON>. Search on Chrome how much time it takes to get from the Empire State Building to this location.",
"In the Notepad app, open the 'memo' file in 'Documents', and check the time and location of the meeting with <PERSON>. Search on Chrome how much time it takes to get from the Empire State Building to this location, and set an appropriate alarm on the Clock app so that I can leave the Empire State Building in time to arrive at the meeting location punctually.",
"In the Notepad app, open the 'travel_plan' file in 'Documents', and check the travel destination. Use Chrome to search if the traffic at the destination drives on the left or the right.",
"Search on Chrome for the dates of International Labour Day and American Independence Day in 2025 respectively, and calculate the interval between the two dates using the Calculator app.",
"Open the 'travel_plan2' file in 'Documents' in the Notepad app, and check the three candidate destinations for the travel plan. Search on Chrome for the flight time from Beijing to each destination, and tell me which candidate destination has the shortest flight time.",
"Search on Chrome for the current stock prices of Nvidia, Apple, and Microsoft respectively. Create a new spreadsheet in Excel, write the company names in column A and the corresponding stock prices in column B.",
"Search on Chrome for the total population of China, the United States, and India in 2024 respectively. Create a new spreadsheet in Excel, write the three countries' names in column A in descending order of population, and the corresponding population numbers in column B.",
"Create a new document in Word. Write down two paragraphs introducing Alibaba and OpenAI respectively. Save the document as 'TechCompanies'.",
"Search for the paper 'Attention is all you need' on Chrome, download the paper and record its abstract. Create a new document in Word, write down the abstract of the paper, and save it as 'Transformer'.",
"Search for the ratings of 'Interstellar' and '12 Angry Men' on imdb.com on Chrome. Open the 'movie_rate' excel file in 'Documents' in File Explorer, and fill in the corresponding movie ratings.",
"Open the 'test_doc1' file in 'Documents' in File Explorer, set the title to be bold, and set the line spacing of the first two paragraphs to 1.5x in Word.",
"Open the 'test_doc2' file in 'Documents' in File Explorer, set the title to be centered, and set the last paragraph to be underlined in Word.",
"Open the 'test_doc3' file in 'Documents' in File Explorer, write down the translation of the content below the main text.",
"Access https://arxiv.org/ in Chrome, search for papers related to 'multimodal agent', and download the first paper.",
"Read the sent mail 'Travel' to Howie in Outlook, record the departure, destination and start date of the journey. Search for a one-way flight on booking.com on Chrome.",
"Search in Chrome for the IMDb ratings of 'Leon: The Professional', 'The Shawshank Redemption', and '2001: A Space Odyssey'. Record them in a new .txt file using Notepad, sorted from highest to lowest.",
"Check the sent mail 'Code' to Howie in Outlook, download the attachment 'homework.py' and open it in Visual Studio Code. Fix the error in this python code.",
"Create a new Python file in Visual Studio Code, write a function that takes a list as input and outputs the k-th largest number in the list. Send this code file to Howie via Outlook.",
"Search for tourist attractions in Tokyo and Kyoto respectively in Chrome, and record the information in a new Word document.",
"Open the 'test_doc3' file located in 'Documents' in File Explorer, note its Chinese content, create a new Word document, and write down the English translation of the Chinese content from test_doc3.",
"Open the 'test_doc1' file located in 'Documents' in File Explorer, increase the font size of the title by one level.",
"In the Notepad app, open the 'travel_plan' file in 'Documents', and check the time and location of the travel plans. Add the travel destination to the World Clock list on the Clock app. Calculate the interval between February 18 and the start time of the travel on the Calculator. ",
"Search on Chrome for the total population of China, the United States, and India in 2024 respectively. Create a new spreadsheet in Excel, write the three countries' names in column A in descending order of population, and the corresponding populations in column B. ",
"Open the 'test_doc1' file in 'Documents' in File Explorer, set the title to be bold, and set the line spacing of the first two paragraphs to 1.5x in Word. ",
"Compare the prices of Amazon, Walmart, and Best Buy for a new Nintendo Switch console in Chrome, and write the site with the cheapest price and the price on Notepad. ",
"Read the mail 'Travel' in Outlook, record the departure, destination and date of the journey. Search for a round-trip flight on booking.com on Chrome. "
